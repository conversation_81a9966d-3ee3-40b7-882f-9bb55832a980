# frozen_string_literal: true

module RequestR<PERSON><PERSON><PERSON><PERSON>
  def response_body
    JSON.parse(response.body, symbolize_names: true).with_indifferent_access
  end

  def response_data
    response_body[:data]
  end

  def get(url, params = {}, headers = {})
    super(url, params:, headers: headers.reverse_merge(
      'Accept' => 'application/json',
      'Content-Type' => 'application/json'
    ))
  end

  %i[post put patch delete].each do |method|
    define_method(method) do |url, params = {}, headers = {}, content_type: 'application/json'|
      body = params.to_json if content_type == 'application/json'

      super(url, params: body, headers: headers.reverse_merge(
        'Accept' => 'application/json',
        'Content-Type' => content_type
      ))
    end
  end

  def expect_response(status, data = nil)
    begin
      expect(response).to have_http_status(status)
    rescue RSpec::Expectations::ExpectationNotMetError => e
      e.message << "\n#{JSON.pretty_generate(response_body)}"
      raise e
    end

    expect(response_body).to be_json_type(data) if data
  end

  def expect_error_response(status = nil, message = nil, details: nil)
    status ||= Integer
    message ||= String
    status = Rack::Utils::SYMBOL_TO_STATUS_CODE[status] || status
    error_format = { errors: [{ status: status, message: message }] }
    error_format[:errors][0][:details] = details if details

    begin
      if status == Integer
        error_message = "expected: 4xx, got: #{response.status}"
        expect(response.client_error?).to eq(true), error_message
      else
        expect(response).to have_http_status(status)
      end
    rescue RSpec::Expectations::ExpectationNotMetError => e
      e.message << "\n#{JSON.pretty_generate(response_body)}"
      raise e
    end

    expect(response_body).to be_json_type(error_format)
    error_message = response_body[:errors][0][:message]

    return unless error_message.is_a?(String)

    expect(error_message).not_to match(/translation missing:/),
                                 error_message
  end

  def as_user(user)
    token = JsonWebToken.encode({ user_id: user.id })
    { 'Authorization' => "Bearer #{token}" }
  end
end
